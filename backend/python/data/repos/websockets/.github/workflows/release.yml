name: Make release

on:
  push:
    tags:
      - '*'
  workflow_dispatch:

jobs:
  sdist:
    name: Build source distribution and architecture-independent wheel
    runs-on: ubuntu-latest
    steps:
    - name: Check out repository
      uses: actions/checkout@v4
    - name: Install Python 3.x
      uses: actions/setup-python@v5
      with:
        python-version: 3.x
    - name: Install build
      run: pip install build
    - name: Build sdist & wheel
      run: python -m build
      env:
        BUILD_EXTENSION: no
    - name: Save sdist & wheel
      uses: actions/upload-artifact@v4
      with:
        name: dist-architecture-independent
        path: |
          dist/*.tar.gz
          dist/*.whl

  wheels:
    name: Build architecture-specific wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:
          - ubuntu-latest
          - windows-latest
          - macOS-latest
    steps:
    - name: Check out repository
      uses: actions/checkout@v4
    - name: Install Python 3.x
      uses: actions/setup-python@v5
      with:
        python-version: 3.x
    - name: Set up QEMU
      if: runner.os == 'Linux'
      uses: docker/setup-qemu-action@v3
      with:
        platforms: all
    - name: Build wheels
      uses: pypa/cibuildwheel@v2.22.0
      env:
        BUILD_EXTENSION: yes
    - name: Save wheels
      uses: actions/upload-artifact@v4
      with:
        name: dist-${{ matrix.os }}
        path: wheelhouse/*.whl

  upload:
    name: Upload
    needs:
      - sdist
      - wheels
    runs-on: ubuntu-latest
    # Don't release when running the workflow manually from GitHub's UI.
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
    permissions:
      id-token: write
      attestations: write
      contents: write
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: dist-*
          merge-multiple: true
          path: dist
      - name: Attest provenance
        uses: actions/attest-build-provenance@v2
        with:
          subject-path: dist/*
      - name: Upload to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
      - name: Create GitHub release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: gh release -R python-websockets/websockets create ${{ github.ref_name }} --notes "See https://websockets.readthedocs.io/en/stable/project/changelog.html for details."

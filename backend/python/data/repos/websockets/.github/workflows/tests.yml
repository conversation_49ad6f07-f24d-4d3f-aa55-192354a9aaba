name: Run tests

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  WEBSOCKETS_TESTS_TIMEOUT_FACTOR: 10

jobs:
  coverage:
    name: Run test coverage checks
    runs-on: ubuntu-latest
    steps:
    - name: Check out repository
      uses: actions/checkout@v4
    - name: Install Python 3.x
      uses: actions/setup-python@v5
      with:
        python-version: "3.x"
    - name: Install tox
      run: pip install tox
    - name: Run tests with coverage
      run: tox -e coverage
    - name: Run tests with per-module coverage
      run: tox -e maxi_cov

  quality:
    name: Run code quality checks
    runs-on: ubuntu-latest
    steps:
    - name: Check out repository
      uses: actions/checkout@v4
    - name: Install Python 3.x
      uses: actions/setup-python@v5
      with:
        python-version: "3.x"
    - name: Install tox
      run: pip install tox
    - name: Check code formatting & style
      run: tox -e ruff
    - name: Check types statically
      run: tox -e mypy

  matrix:
    name: Run tests on Python ${{ matrix.python }}
    needs:
      - coverage
      - quality
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python:
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
          - "3.13"
          - "pypy-3.10"
        is_main:
          - ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
        exclude:
          - python: "pypy-3.10"
            is_main: false
    steps:
    - name: Check out repository
      uses: actions/checkout@v4
    - name: Install Python ${{ matrix.python }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python }}
        allow-prereleases: true
    - name: Install tox
      run: pip install tox
    - name: Run tests
      run: tox -e py

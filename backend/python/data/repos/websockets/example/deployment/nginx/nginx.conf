daemon off;

events {
}

http {
    server {
        listen localhost:8080;

        location / {
            proxy_http_version 1.1;
            proxy_pass http://websocket;
            proxy_set_header Connection $http_connection;
            proxy_set_header Upgrade $http_upgrade;
        }
    }

    upstream websocket {
        least_conn;
        server unix:websockets-test_00.sock;
        server unix:websockets-test_01.sock;
        server unix:websockets-test_02.sock;
        server unix:websockets-test_03.sock;
    }
}

apiVersion: v1
kind: Service
metadata:
  name: websockets-test
spec:
  type: NodePort
  ports:
    - port: 80
      nodePort: 32080
  selector:
    app: websockets-test
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: websockets-test
spec:
  selector:
    matchLabels:
      app: websockets-test
  template:
    metadata:
      labels:
        app: websockets-test
    spec:
      containers:
      - name: websockets-test
        image: websockets-test:1.0
        livenessProbe:
          httpGet:
            path: /healthz
            port: 80
          periodSeconds: 1
        ports:
        - containerPort: 80

[tox]
env_list =
    py39
    py310
    py311
    py312
    py313
    coverage
    ruff
    mypy

[testenv]
commands =
    python -W error::DeprecationWarning -W error::PendingDeprecationWarning -m unittest {posargs}
pass_env =
    WEBSOCKETS_*
deps =
    py311,py312,py313,coverage,maxi_cov: mitmproxy
    py311,py312,py313,coverage,maxi_cov: python-socks[asyncio]
    werkzeug

[testenv:coverage]
commands =
    python -m coverage run --source {envsitepackagesdir}/websockets,tests -m unittest {posargs}
    python -m coverage report --show-missing --fail-under=100
deps =
    coverage
    {[testenv]deps}

[testenv:maxi_cov]
commands =
    python tests/maxi_cov.py {envsitepackagesdir}
    python -m coverage report --show-missing --fail-under=100
deps =
    coverage
    {[testenv]deps}

[testenv:ruff]
commands =
    ruff format --check src tests
    ruff check src tests
deps =
    ruff

[testenv:mypy]
commands =
    mypy --strict src
deps =
    mypy
    python-socks
    werkzeug

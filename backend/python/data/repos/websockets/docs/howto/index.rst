How-to guides
=============

Set up your development environment comfortably.

.. toctree::

   autoreload
   debugging

Configure websockets securely in production.

.. toctree::

   encryption

These guides will help you design and build your application.

.. toctree::
   :maxdepth: 2

   patterns
   django

Upgrading from the legacy :mod:`asyncio` implementation to the new one?
Read this.

.. toctree::
   :maxdepth: 2

   upgrade

If you're integrating the Sans-I/O layer of websockets into a library, rather
than building an application with websockets, follow this guide.

.. toctree::
   :maxdepth: 2

   sansio

The WebSocket protocol makes provisions for extending or specializing its
features, which websockets supports fully.

.. toctree::

   extensions

Getting support
===============

.. admonition:: There are no free support channels.
    :class: tip

    websockets is an open-source project. It's primarily maintained by one
    person as a hobby.

    For this reason, the focus is on flawless code and self-service
    documentation, not support.

Enterprise
----------

websockets is maintained with high standards, making it suitable for enterprise
use cases. Additional guarantees are available via :doc:`Tidelift <tidelift>`.
If you're using it in a professional setting, consider subscribing.

Questions
---------

GitHub issues aren't a good medium for handling questions. There are better
places to ask questions, for example Stack Overflow.

If you want to ask a question anyway, please make sure that:

- it's a question about websockets and not about :mod:`asyncio`;
- it isn't answered in the documentation;
- it wasn't asked already.

A good question can be written as a suggestion to improve the documentation.

Cryptocurrency users
--------------------

websockets appears to be quite popular for interfacing with Bitcoin or other
cryptocurrency trackers. I'm strongly opposed to Bitcoin's carbon footprint.

I'm aware of efforts to build proof-of-stake models. I'll care once the total
energy consumption of all cryptocurrencies drops to a non-bullshit level.

You already negated all of humanity's efforts to develop renewable energy.
Please stop heating the planet where my children will have to live.

Since websockets is released under an open-source license, you can use it for
any purpose you like. However, I won't spend any of my time to help you.

I will summarily close issues related to cryptocurrency in any way.

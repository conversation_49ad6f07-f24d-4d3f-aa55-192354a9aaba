Getting started
===============

.. currentmodule:: websockets

Requirements
------------

websockets requires Python ≥ 3.9.

.. admonition:: Use the most recent Python release
    :class: tip

    For each minor version (3.x), only the latest bugfix or security release
    (3.x.y) is officially supported.

It doesn't have any dependencies.

.. _install:

Installation
------------

Install websockets with:

.. code-block:: console

    $ pip install websockets

Wheels are available for all platforms.

Tutorial
--------

Learn how to build an real-time web application with websockets.

.. toctree::
    :maxdepth: 2

    tutorial1
    tutorial2
    tutorial3

In a hurry?
-----------

These examples will get you started quickly with websockets.

.. toctree::
    :maxdepth: 2

    examples

@article{https://doi.org/10.1002/(SICI)1521-4133(199812)100:12<524::AID-LIPI524>3.0.CO;2-6,
author = {<PERSON><PERSON>, <PERSON>},
title = {Umweltrelevante Kriterien zur Anwendung von Pflanzenölen und deren Derivaten im Schmierstoffbereich},
journal = {Lipid / Fett},
volume = {100},
number = {12},
pages = {524-527},
doi = {https://doi.org/10.1002/(SICI)1521-4133(199812)100:12<524::AID-LIPI524>3.0.CO;2-6},
url = {https://onlinelibrary.wiley.com/doi/abs/10.1002/%28SICI%291521-4133%28199812%29100%3A12%3C524%3A%3AAID-LIPI524%3E3.0.CO%3B2-6},
eprint = {https://onlinelibrary.wiley.com/doi/pdf/10.1002/%28SICI%291521-4133%28199812%29100%3A12%3C524%3A%3AAID-LIPI524%3E3.0.CO%3B2-6},
abstract = {Abstract Der Einsatz nachwachsender Rohstoffe und deren Derivate in Schmierstoffen wird durch ihre besondere Umweltverträglichkeit motiviert, wobei die Substitution von Mineralöl durch biologisch abbaubare Grundöle im Vordergrund steht. Inzwischen werden für nahezu alle Schmierstoffanwendungen umweltfreundliche, biologisch abbaubare Alternativen zu den herkömmlichen Mineralölprodukten angeboten. 1997 wurden in Deutschland ca. 40000 t biologisch schnell abbaubare Schmierstoffe abgesetzt, also etwa 4,5\% der gesamten Schmierstoffmenge. Die weitere Steigerung dieses Anteils ist Ziel verschiedener Maßnahmen von Regierungen und Behörden. Allgemein ist anerkannt, daß potentiell mehr als 90\% aller Schmierstoffe auf Basis nachwachsender Rohstoffe dargestellt werden können.},
year = {1998}
}
@inproceedings{shen2021layoutparser,
  title        = {LayoutParser: A unified toolkit for deep learning based document image analysis},
  author       = {Shen, Zejiang and Zhang, Ruochen and Dell, Melissa and Lee, Benjamin Charles Germain and Carlson, Jacob and Li, Weining},
  booktitle    = {Document Analysis and Recognition--ICDAR 2021: 16th International Conference, Lausanne, Switzerland, September 5--10, 2021, Proceedings, Part I 16},
  pages        = {131--146},
  year         = {2021},
  organization = {Springer},
  editor       = {Llad{\'o}s, Josep
                  and Lopresti, Daniel
                  and Uchida, Seiichi},
  file         = {layout-parser-paper.pdf},
  abstract     = {{Recent advances in document image analysis (DIA) have been primarily driven by the application of neural networks. Ideally, research outcomes could be easily deployed in production and extended for further investigation. However, various factors like loosely organized codebases and sophisticated model configurations complicate the easy reuse of important innovations by a wide audience. Though there have been on-going efforts to improve reusability and simplify deep learning (DL) model development in disciplines like natural language processing and computer vision, none of them are optimized for challenges in the domain of DIA. This represents a major gap in the existing toolkit, as DIA is central to academic research across a wide range of disciplines in the social sciences and humanities. This paper introduces LayoutParser, an open-source library for streamlining the usage of DL in DIA research and applications. The core LayoutParser library comes with a set of simple and intuitive interfaces for applying and customizing DL models for layout detection, character recognition, and many other document processing tasks. To promote extensibility, LayoutParser also incorporates a community platform for sharing both pre-trained models and full document digitization pipelines. We demonstrate that LayoutParser is helpful for both lightweight and large-scale digitization pipelines in real-word use cases. The library is publicly available at https://layout-parser.github.io.",
                  isbn="978-3-030-86549-8}},
}
@inproceedings{10.1145/3652037.3663916,
author = {Maganaris, Constantine and Protopapadakis, Eftychios and Doulamis, Nikolaos},
title = {Outlier detection in maritime environments using AIS data and deep recurrent architectures},
year = {2024},
isbn = {9798400717604},
publisher = {Association for Computing Machinery},
address = {New York, NY, USA},
url = {https://doi.org/10.1145/3652037.3663916},
doi = {10.1145/3652037.3663916},
booktitle = {Proceedings of the 17th International Conference on PErvasive Technologies Related to Assistive Environments},
pages = {420–427},
numpages = {8},
keywords = {AI, AIS, GRU, RNN, datasets, deep learning, maritime, neural networks, outlier detection, segmentation},
location = {Crete, Greece},
series = {PETRA '24}
}
@INBOOK{inbook-full,
   author = "Knuth | Donald E. ",
   title = "Fundamental Algorithms",
   volume = "1",
   series = "The Art of Computer Programming",
   publisher = "Addison-Wesley",
   address = "Reading Massachusetts",
   edition = "Second",
   month = "10 jan",
   year = "{\noopsort{1973b}}1973",
   type = "Section",
   chapter = "1.2",
   pages = "10 119",
   note = "This is a full INBOOK entry",
}


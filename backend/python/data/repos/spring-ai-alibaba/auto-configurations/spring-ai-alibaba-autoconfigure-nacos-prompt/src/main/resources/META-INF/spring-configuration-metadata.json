{"groups": [{"name": "spring.ai.alibaba.mcp.nacos", "type": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "description": "Configuration properties for Nacos MCP integration."}, {"name": "spring.ai.alibaba.mcp.nacos.registry", "type": "com.alibaba.cloud.ai.mcp.nacos.registry.NacosMcpRegistryProperties", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.registry.NacosMcpRegistryProperties", "description": "Configuration properties for Nacos MCP server registration."}, {"name": "spring.ai.alibaba.mcp.nacos.dynamic", "type": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.config.NacosMcpDynamicProperties", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.config.NacosMcpDynamicProperties", "description": "Configuration properties for Nacos MCP dynamic service discovery."}, {"name": "spring.ai.nacos.prompt.template", "type": "com.alibaba.cloud.ai.autoconfigure.prompt.NacosPromptTmplProperties", "sourceType": "com.alibaba.cloud.ai.autoconfigure.prompt.NacosPromptTmplProperties", "description": "Configuration properties for Nacos prompt template integration."}], "properties": [{"name": "spring.ai.alibaba.mcp.nacos.server-addr", "type": "java.lang.String", "description": "Nacos server address.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": "localhost:8848"}, {"name": "spring.ai.alibaba.mcp.nacos.namespace", "type": "java.lang.String", "description": "Nacos namespace for MCP service.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": ""}, {"name": "spring.ai.alibaba.mcp.nacos.username", "type": "java.lang.String", "description": "Nacos username to authenticate.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": ""}, {"name": "spring.ai.alibaba.mcp.nacos.password", "type": "java.lang.String", "description": "Nacos password to authenticate.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": ""}, {"name": "spring.ai.alibaba.mcp.nacos.access-key", "type": "java.lang.String", "description": "Nacos access key to authenticate.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": ""}, {"name": "spring.ai.alibaba.mcp.nacos.secret-key", "type": "java.lang.String", "description": "Nacos secret key to authenticate.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": ""}, {"name": "spring.ai.alibaba.mcp.nacos.endpoint", "type": "java.lang.String", "description": "Nacos server endpoint.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.NacosMcpProperties", "defaultValue": ""}, {"name": "spring.ai.alibaba.mcp.nacos.registry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to register MCP server service to Nacos.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.registry.NacosMcpRegistryProperties", "defaultValue": false}, {"name": "spring.ai.alibaba.mcp.nacos.registry.service-name", "type": "java.lang.String", "description": "Service name for MCP server service.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.registry.NacosMcpRegistryProperties", "defaultValue": "mcp-server"}, {"name": "spring.ai.alibaba.mcp.nacos.registry.service-group", "type": "java.lang.String", "description": "Group for MCP server service.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.registry.NacosMcpRegistryProperties", "defaultValue": "DEFAULT_GROUP"}, {"name": "spring.ai.alibaba.mcp.nacos.registry.sse-export-context-path", "type": "java.lang.String", "description": "Base context path for SSE MCP server.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.registry.NacosMcpRegistryProperties", "defaultValue": "/mcp"}, {"name": "spring.ai.alibaba.mcp.nacos.dynamic.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable dynamic discovery of MCP server info from Nacos.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.config.NacosMcpDynamicProperties", "defaultValue": false}, {"name": "spring.ai.alibaba.mcp.nacos.dynamic.service-group", "type": "java.lang.String", "description": "Service group for dynamic discovery of MCP servers.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.config.NacosMcpDynamicProperties", "defaultValue": "DEFAULT_GROUP"}, {"name": "spring.ai.alibaba.mcp.nacos.dynamic.service-names", "type": "java.util.List<java.lang.String>", "description": "Service names for dynamic discovery of MCP servers.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.config.NacosMcpDynamicProperties"}, {"name": "spring.ai.mcp.server.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable MCP server.", "defaultValue": true}, {"name": "spring.ai.alibaba.mcp.server.max-connections", "type": "java.lang.Integer", "description": "Maximum number of HTTP connections for the MCP server.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.properties.McpDynamicServerProperties", "defaultValue": 50}, {"name": "spring.ai.alibaba.mcp.server.acquire-timeout", "type": "java.lang.Integer", "description": "Timeout in milliseconds for acquiring a connection.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.properties.McpDynamicServerProperties", "defaultValue": 30000}, {"name": "spring.ai.alibaba.mcp.server.max-idle-time", "type": "java.lang.Integer", "description": "Maximum idle time in seconds for connections.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.properties.McpDynamicServerProperties", "defaultValue": 30}, {"name": "spring.ai.alibaba.mcp.server.max-life-time", "type": "java.lang.Integer", "description": "Maximum lifetime in seconds for connections.", "sourceType": "com.alibaba.cloud.ai.mcp.nacos.dynamic.server.properties.McpDynamicServerProperties", "defaultValue": 120}, {"name": "spring.ai.nacos.prompt.template.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable Nacos prompt template support.", "sourceType": "com.alibaba.cloud.ai.autoconfigure.prompt.NacosPromptTmplProperties", "defaultValue": false}], "hints": []}